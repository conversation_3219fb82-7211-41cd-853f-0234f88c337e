# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
input_videos/
output_videos/
temp/
*.log
*.wav
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv
*.webm

# Model files
models/
*.pt
*.pth
*.bin
*.safetensors

# Temporary files
*.tmp
*.temp
.cache/

# Configuration (if contains sensitive data)
# config.yaml
