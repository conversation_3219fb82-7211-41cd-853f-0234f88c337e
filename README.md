# Video Dubbing Pipeline

A clean and minimal video dubbing pipeline that automatically generates Hindi dubbing for videos using AI models. The pipeline runs locally using open-source tools and provides real-time progress feedback.

## Features

- 🎵 **Audio Extraction**: Extract audio from videos using FFmpeg with optimal settings for speech recognition
- 🎤 **Speech-to-Text**: Transcribe audio using faster-whisper for accurate speech recognition
- 🌐 **Translation**: Translate transcriptions to Hindi using local translation models (Argos Translate)
- 🗣️ **Voice Cloning**: Generate natural-sounding Hindi speech using Coqui TTS with voice cloning
- 🎬 **Video Processing**: Combine dubbed audio with original video while preserving video quality
- 📊 **Progress Tracking**: Real-time progress feedback and comprehensive logging
- 🔧 **Easy Configuration**: YAML-based configuration for easy customization

## Requirements

### System Dependencies
- **FFmpeg**: Required for audio/video processing
  - Ubuntu/Debian: `sudo apt install ffmpeg`
  - macOS: `brew install ffmpeg`
  - Windows: Download from [ffmpeg.org](https://ffmpeg.org/download.html)

### Python Dependencies
- Python 3.8 or higher
- See `requirements.txt` for Python packages

## Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd video-pipeline
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify FFmpeg installation**
   ```bash
   ffmpeg -version
   ```

## Quick Start

1. **Place your video files** in the `input_videos/` directory

2. **Run the pipeline**
   ```bash
   python main.py
   ```

3. **Find dubbed videos** in the `output_videos/` directory

## Usage

### Basic Usage
```bash
# Process all videos in input_videos/ directory
python main.py

# Process videos from a custom directory
python main.py --input /path/to/videos/

# Process a single video file
python main.py --single video.mp4

# Check pipeline status
python main.py --status
```

### Advanced Usage
```bash
# Use custom configuration
python main.py --config custom_config.yaml

# Enable verbose logging
python main.py --verbose
```

## Configuration

Edit `config.yaml` to customize the pipeline:

```yaml
# Directories
input_dir: "input_videos"
output_dir: "output_videos"
temp_dir: "temp"

# Whisper settings
whisper:
  model_size: "base"  # tiny, base, small, medium, large-v2, large-v3
  device: "cpu"       # cpu, cuda
  language: "en"      # Source language

# Translation settings
translation:
  source_lang: "en"
  target_lang: "hi"   # Hindi

# TTS settings
tts:
  model: "tts_models/multilingual/multi-dataset/xtts_v2"
  language: "hi"
  speed: 1.0
```

## Pipeline Steps

1. **Audio Extraction**: Extract audio from video using FFmpeg with 16kHz sample rate
2. **Speech Recognition**: Transcribe audio to text using faster-whisper
3. **Translation**: Translate English text to Hindi using Argos Translate
4. **Voice Synthesis**: Generate Hindi speech using Coqui TTS with voice cloning
5. **Video Assembly**: Combine dubbed audio with original video using FFmpeg

## Supported Formats

### Input Video Formats
- MP4, AVI, MOV, MKV, WMV, FLV, WebM

### Output
- MP4 with AAC audio and original video codec (no re-encoding)

## Performance Tips

- **GPU Acceleration**: Set `whisper.device: "cuda"` if you have a CUDA-compatible GPU
- **Model Size**: Use smaller Whisper models (`tiny`, `base`) for faster processing
- **Batch Processing**: Process multiple videos automatically by placing them in the input directory

## Troubleshooting

### Common Issues

1. **FFmpeg not found**
   - Install FFmpeg and ensure it's in your system PATH
   - Test with: `ffmpeg -version`

2. **CUDA out of memory**
   - Set `whisper.device: "cpu"` in config.yaml
   - Use smaller model: `whisper.model_size: "base"`

3. **Translation not working**
   - The pipeline will automatically download required translation models on first use
   - Ensure internet connection for initial setup

4. **Poor voice quality**
   - Ensure input audio is clear and has minimal background noise
   - Try different TTS models in the configuration

### Logs
Check `dubbing_pipeline.log` for detailed error information.

## File Structure

```
video-pipeline/
├── main.py                 # Main entry point
├── config.yaml            # Configuration file
├── requirements.txt       # Python dependencies
├── README.md              # This file
├── src/                   # Source code
│   ├── __init__.py
│   ├── pipeline.py        # Main pipeline controller
│   ├── audio_extractor.py # Audio extraction module
│   ├── speech_to_text.py  # Speech recognition module
│   ├── translator.py      # Translation module
│   ├── text_to_speech.py  # TTS module
│   ├── video_processor.py # Video processing module
│   └── utils.py           # Utility functions
├── input_videos/          # Place input videos here
├── output_videos/         # Dubbed videos appear here
└── temp/                  # Temporary files (auto-cleaned)
```

## License

This project uses open-source components:
- faster-whisper (MIT License)
- Coqui TTS (MPL 2.0 License)
- Argos Translate (MIT License)
- FFmpeg (LGPL/GPL License)

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## Acknowledgments

- OpenAI Whisper team for the speech recognition model
- Coqui team for the TTS models
- Argos Translate team for local translation
- FFmpeg team for multimedia processing
