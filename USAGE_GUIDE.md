# Video Dubbing Pipeline - Usage Guide

## Quick Start

### 1. Installation
```bash
# Install dependencies
python install.py

# Or manually:
pip install -r requirements.txt
```

### 2. Basic Usage
```bash
# Place videos in input_videos/ directory
cp your_video.mp4 input_videos/

# Run the pipeline
python main.py

# Check results in output_videos/
ls output_videos/
```

## Detailed Usage

### Command Line Options

```bash
# Process all videos in input directory
python main.py

# Process specific directory
python main.py --input /path/to/videos/

# Process single video
python main.py --single video.mp4

# Check system status
python main.py --status

# Use custom configuration
python main.py --config custom_config.yaml

# Enable verbose logging
python main.py --verbose
```

### Configuration Options

Edit `config.yaml` to customize behavior:

#### Whisper Settings
```yaml
whisper:
  model_size: "base"    # tiny, base, small, medium, large-v2, large-v3
  device: "cpu"         # cpu, cuda (for GPU acceleration)
  compute_type: "int8"  # int8, int16, float16, float32
  language: "en"        # Source language
```

#### Translation Settings
```yaml
translation:
  source_lang: "en"     # English
  target_lang: "hi"     # Hindi
  model: "argos"        # Local translation model
```

#### TTS Settings
```yaml
tts:
  model: "tts_models/multilingual/multi-dataset/xtts_v2"
  language: "hi"        # Hindi
  speaker_wav: null     # Path to reference audio for voice cloning
  speed: 1.0           # Speech speed multiplier
```

## Performance Optimization

### For Faster Processing
- Use smaller Whisper models: `model_size: "tiny"` or `"base"`
- Enable GPU acceleration: `device: "cuda"` (requires CUDA)
- Use lower quality TTS models for testing

### For Better Quality
- Use larger Whisper models: `model_size: "large-v3"`
- Provide clean reference audio for voice cloning
- Ensure input videos have clear audio

## Troubleshooting

### Common Issues

1. **"FFmpeg not found"**
   ```bash
   # Install FFmpeg
   # Ubuntu/Debian:
   sudo apt install ffmpeg
   
   # macOS:
   brew install ffmpeg
   
   # Windows: Download from ffmpeg.org
   ```

2. **"CUDA out of memory"**
   ```yaml
   # In config.yaml, change to:
   whisper:
     device: "cpu"
     model_size: "base"
   ```

3. **"Translation package not found"**
   - The pipeline automatically downloads translation models on first use
   - Ensure internet connection for initial setup

4. **Poor audio quality**
   - Check input video audio quality
   - Ensure minimal background noise
   - Try different TTS models

### Log Files
- Check `dubbing_pipeline.log` for detailed error information
- Use `--verbose` flag for more detailed console output

## File Structure

```
video-pipeline/
├── input_videos/          # Place your videos here
├── output_videos/         # Dubbed videos appear here
├── temp/                  # Temporary files (auto-cleaned)
├── config.yaml           # Main configuration
├── main.py               # Main script
├── test_pipeline.py      # Test script
└── src/                  # Source code modules
```

## Testing

### Test Installation
```bash
python test_pipeline.py
```

### Create Test Video
```bash
# The test script can create a simple test video
python test_pipeline.py
# Follow prompts to create test video

# Then test the pipeline
python main.py --single test_video.mp4
```

## Advanced Usage

### Custom Voice Cloning
1. Extract a clean audio sample from the original video
2. Save it as a WAV file
3. Update config.yaml:
   ```yaml
   tts:
     speaker_wav: "path/to/speaker_sample.wav"
   ```

### Batch Processing
- Place multiple videos in `input_videos/`
- Run `python main.py`
- All videos will be processed automatically

### Custom Languages
- Modify `source_lang` and `target_lang` in config.yaml
- Ensure translation models support your language pair
- Update TTS language setting accordingly

## Tips for Best Results

1. **Input Video Quality**
   - Use videos with clear, single-speaker audio
   - Minimize background noise and music
   - Ensure good audio levels (not too quiet/loud)

2. **Processing Environment**
   - Use SSD storage for faster I/O
   - Close other applications to free up memory
   - Use GPU acceleration when available

3. **Voice Cloning**
   - Provide 10-30 seconds of clean speaker audio
   - Use audio from the same video for consistency
   - Avoid segments with background noise

4. **Monitoring Progress**
   - Watch console output for progress updates
   - Check log files for detailed information
   - Use `--status` to verify system readiness
