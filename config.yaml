# Video Dubbing Pipeline Configuration

# Directories
input_dir: "input_videos"
output_dir: "output_videos"
temp_dir: "temp"

# Audio extraction settings
audio:
  sample_rate: 16000
  format: "wav"
  channels: 1

# Faster Whisper settings
whisper:
  model_size: "base"  # tiny, base, small, medium, large-v2, large-v3
  device: "cpu"  # cpu, cuda
  compute_type: "int8"  # int8, int16, float16, float32
  language: "en"  # Source language

# Translation settings
translation:
  source_lang: "en"
  target_lang: "hi"  # Hindi
  model: "argos"  # argos for local translation

# Text-to-Speech settings
tts:
  model: "tts_models/multilingual/multi-dataset/xtts_v2"
  language: "hi"
  speaker_wav: null  # Path to reference speaker audio for voice cloning
  speed: 1.0

# Video processing
video:
  keep_original_video: true
  audio_codec: "aac"
  video_codec: "copy"  # Don't re-encode video

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "dubbing_pipeline.log"
