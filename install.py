#!/usr/bin/env python3
"""
Installation script for Video Dubbing Pipeline.
Checks system requirements and installs dependencies.
"""

import sys
import subprocess
import platform
from pathlib import Path

def print_colored(message, color_code):
    """Print colored message."""
    print(f"\033[{color_code}m{message}\033[0m")

def print_info(message):
    print_colored(f"[INFO] {message}", "36")  # <PERSON>an

def print_success(message):
    print_colored(f"[SUCCESS] {message}", "32")  # Green

def print_error(message):
    print_colored(f"[ERROR] {message}", "31")  # Red

def print_warning(message):
    print_colored(f"[WARNING] {message}", "33")  # Yellow

def check_python_version():
    """Check if Python version is compatible."""
    print_info("Checking Python version...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print_success(f"Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print_error(f"Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print_error("Python 3.8 or higher is required")
        return False

def check_ffmpeg():
    """Check if FFmpeg is installed."""
    print_info("Checking FFmpeg...")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("FFmpeg is installed")
            return True
        else:
            print_error("FFmpeg is not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("FFmpeg is not installed")
        return False

def install_ffmpeg_instructions():
    """Provide FFmpeg installation instructions."""
    print_warning("FFmpeg installation required:")
    
    system = platform.system().lower()
    
    if system == "linux":
        print_info("Ubuntu/Debian: sudo apt update && sudo apt install ffmpeg")
        print_info("CentOS/RHEL: sudo yum install ffmpeg")
        print_info("Arch Linux: sudo pacman -S ffmpeg")
    elif system == "darwin":  # macOS
        print_info("macOS with Homebrew: brew install ffmpeg")
        print_info("macOS with MacPorts: sudo port install ffmpeg")
    elif system == "windows":
        print_info("Windows: Download from https://ffmpeg.org/download.html")
        print_info("Or use Chocolatey: choco install ffmpeg")
    else:
        print_info("Visit https://ffmpeg.org/download.html for installation instructions")

def install_python_dependencies():
    """Install Python dependencies."""
    print_info("Installing Python dependencies...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print_error("requirements.txt not found")
        return False
    
    try:
        # Upgrade pip first
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True)
        
        # Install requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        
        print_success("Python dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print_error(f"Failed to install Python dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories."""
    print_info("Creating directories...")
    
    directories = ["input_videos", "output_videos", "temp"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print_success(f"Created directory: {directory}")

def test_installation():
    """Test the installation."""
    print_info("Testing installation...")
    
    try:
        # Test imports
        import faster_whisper
        import TTS
        import argostranslate
        import moviepy
        import pydub
        
        print_success("All Python packages imported successfully")
        
        # Test pipeline initialization
        sys.path.insert(0, str(Path(__file__).parent / 'src'))
        from src.pipeline import VideoDubbingPipeline
        
        pipeline = VideoDubbingPipeline('config.yaml')
        print_success("Pipeline initialized successfully")
        
        return True
        
    except ImportError as e:
        print_error(f"Import error: {e}")
        return False
    except Exception as e:
        print_error(f"Pipeline test failed: {e}")
        return False

def main():
    """Main installation function."""
    print_info("🎬 Video Dubbing Pipeline Installation")
    print_info("=" * 50)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Check FFmpeg
    if not check_ffmpeg():
        install_ffmpeg_instructions()
        success = False
    
    if not success:
        print_error("Please fix the above issues before continuing")
        return 1
    
    # Install Python dependencies
    if not install_python_dependencies():
        return 1
    
    # Create directories
    create_directories()
    
    # Test installation
    if test_installation():
        print_success("🎉 Installation completed successfully!")
        print_info("\nNext steps:")
        print_info("1. Place video files in the 'input_videos' directory")
        print_info("2. Run: python main.py")
        print_info("3. Find dubbed videos in the 'output_videos' directory")
        print_info("\nFor help: python main.py --help")
        return 0
    else:
        print_error("Installation test failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
