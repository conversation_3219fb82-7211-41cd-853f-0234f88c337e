#!/usr/bin/env python3
"""
Video Dubbing Pipeline - Main Entry Point

A clean and minimal video dubbing pipeline that processes videos to generate Hindi dubbing using AI models.

Features:
- Extract audio using ffmpeg
- Transcribe using faster-whisper
- Translate using local translation models
- Generate speech using voice cloning
- Combine dubbed audio with original video

Usage:
    python main.py [options]
    
Examples:
    python main.py                          # Process all videos in input_videos/
    python main.py --input custom_input/    # Process videos from custom directory
    python main.py --single video.mp4       # Process single video file
    python main.py --status                 # Check pipeline status
"""

import argparse
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.pipeline import VideoDubbingPipeline
from src.utils import print_progress, print_success, print_error, print_warning

def main():
    """Main entry point for the video dubbing pipeline."""
    parser = argparse.ArgumentParser(
        description="Video Dubbing Pipeline - Generate Hindi dubbing for videos using AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                              Process all videos in input_videos/
  %(prog)s --input custom_input/        Process videos from custom directory  
  %(prog)s --single video.mp4           Process single video file
  %(prog)s --config custom_config.yaml Use custom configuration file
  %(prog)s --status                     Check pipeline component status
        """
    )
    
    parser.add_argument(
        '--input', '-i',
        type=str,
        help='Input directory containing video files (default: from config)'
    )
    
    parser.add_argument(
        '--single', '-s',
        type=str,
        help='Process a single video file'
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config.yaml',
        help='Configuration file path (default: config.yaml)'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='Check pipeline component status and exit'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Print banner
    print_progress("🎬 Video Dubbing Pipeline")
    print_progress("=" * 50)
    
    try:
        # Initialize pipeline
        print_progress("Initializing pipeline...")
        pipeline = VideoDubbingPipeline(args.config)
        
        # Check status if requested
        if args.status:
            print_progress("Checking pipeline status...")
            status = pipeline.get_pipeline_status()
            
            print_progress("\nComponent Status:")
            print_progress("-" * 30)
            
            # FFmpeg
            if status['ffmpeg_available']:
                print_success("✓ FFmpeg: Available")
            else:
                print_error("✗ FFmpeg: Not found (required for audio/video processing)")
            
            # Whisper
            if status['whisper_model_loaded']:
                print_success("✓ Whisper: Model loaded")
            else:
                print_warning("⚠ Whisper: Model not loaded (will load on first use)")
            
            # TTS
            if status['tts_model_loaded']:
                print_success("✓ TTS: Model loaded")
            else:
                print_warning("⚠ TTS: Model not loaded (will load on first use)")
            
            # Translation
            if status['translation_ready']:
                print_success("✓ Translation: Ready")
            else:
                print_warning("⚠ Translation: Not ready (will setup on first use)")
            
            return 0
        
        # Process single video
        if args.single:
            video_path = Path(args.single)
            if not video_path.exists():
                print_error(f"Video file not found: {video_path}")
                return 1
            
            print_progress(f"Processing single video: {video_path}")
            success = pipeline.process_single_video(video_path)
            
            if success:
                print_success("Single video processing completed successfully!")
                return 0
            else:
                print_error("Single video processing failed!")
                return 1
        
        # Process directory
        else:
            print_progress("Processing video directory...")
            results = pipeline.process_directory(args.input)
            
            if results['processed'] > 0:
                print_success(f"Successfully processed {results['processed']} videos!")
            
            if results['failed'] > 0:
                print_error(f"Failed to process {results['failed']} videos!")
                return 1
            
            if results['total'] == 0:
                print_warning("No videos found to process!")
                return 1
            
            return 0
    
    except KeyboardInterrupt:
        print_warning("\nProcessing interrupted by user")
        return 1
    
    except FileNotFoundError as e:
        print_error(f"File not found: {e}")
        return 1
    
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

def check_dependencies():
    """Check if required dependencies are installed."""
    missing_deps = []
    
    try:
        import faster_whisper
    except ImportError:
        missing_deps.append("faster-whisper")
    
    try:
        import TTS
    except ImportError:
        missing_deps.append("TTS")
    
    try:
        import argostranslate
    except ImportError:
        missing_deps.append("argostranslate")
    
    try:
        import moviepy
    except ImportError:
        missing_deps.append("moviepy")
    
    try:
        import pydub
    except ImportError:
        missing_deps.append("pydub")
    
    if missing_deps:
        print_error("Missing required dependencies:")
        for dep in missing_deps:
            print_error(f"  - {dep}")
        print_progress("\nInstall missing dependencies with:")
        print_progress("  pip install -r requirements.txt")
        return False
    
    return True

if __name__ == "__main__":
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    # Run main function
    sys.exit(main())
