"""
Audio extraction module for video dubbing pipeline.
Extracts audio from video files using ffmpeg with optimal settings for faster-whisper.
"""

import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from .utils import print_progress, print_success, print_error

class AudioExtractor:
    """Handles audio extraction from video files using ffmpeg."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize AudioExtractor with configuration."""
        self.config = config
        self.audio_config = config.get('audio', {})
        self.temp_dir = Path(config.get('temp_dir', 'temp'))
        self.logger = logging.getLogger('video_dubbing.audio_extractor')
        
        # Ensure temp directory exists
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def check_ffmpeg(self) -> bool:
        """Check if ffmpeg is available."""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def extract_audio(self, video_path: Path) -> Optional[Path]:
        """
        Extract audio from video file.
        
        Args:
            video_path: Path to input video file
            
        Returns:
            Path to extracted audio file or None if extraction failed
        """
        if not self.check_ffmpeg():
            print_error("ffmpeg not found. Please install ffmpeg.")
            self.logger.error("ffmpeg not found")
            return None
        
        # Generate output audio filename
        audio_filename = f"{video_path.stem}_audio.{self.audio_config.get('format', 'wav')}"
        audio_path = self.temp_dir / audio_filename
        
        # Build ffmpeg command
        sample_rate = self.audio_config.get('sample_rate', 16000)
        channels = self.audio_config.get('channels', 1)
        
        cmd = [
            'ffmpeg',
            '-i', str(video_path),
            '-vn',  # No video
            '-acodec', 'pcm_s16le',  # PCM 16-bit little-endian
            '-ar', str(sample_rate),  # Sample rate
            '-ac', str(channels),  # Number of channels
            '-y',  # Overwrite output file
            str(audio_path)
        ]
        
        print_progress(f"Extracting audio from {video_path.name}...")
        self.logger.info(f"Extracting audio: {' '.join(cmd)}")
        
        try:
            # Run ffmpeg command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes timeout
            )
            
            if result.returncode == 0:
                print_success(f"Audio extracted to {audio_path}")
                self.logger.info(f"Audio extraction successful: {audio_path}")
                return audio_path
            else:
                print_error(f"Audio extraction failed: {result.stderr}")
                self.logger.error(f"ffmpeg error: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print_error("Audio extraction timed out")
            self.logger.error("Audio extraction timed out")
            return None
        except Exception as e:
            print_error(f"Audio extraction error: {str(e)}")
            self.logger.error(f"Audio extraction error: {str(e)}")
            return None
    
    def get_audio_info(self, video_path: Path) -> Optional[Dict[str, Any]]:
        """
        Get audio information from video file.
        
        Args:
            video_path: Path to video file
            
        Returns:
            Dictionary with audio information or None if failed
        """
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_streams',
            '-select_streams', 'a:0',  # First audio stream
            str(video_path)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                if data.get('streams'):
                    stream = data['streams'][0]
                    return {
                        'codec': stream.get('codec_name'),
                        'sample_rate': int(stream.get('sample_rate', 0)),
                        'channels': int(stream.get('channels', 0)),
                        'duration': float(stream.get('duration', 0))
                    }
            return None
        except Exception as e:
            self.logger.error(f"Error getting audio info: {str(e)}")
            return None
    
    def cleanup_temp_audio(self, audio_path: Path) -> None:
        """Clean up temporary audio file."""
        try:
            if audio_path.exists():
                audio_path.unlink()
                self.logger.info(f"Cleaned up temporary audio file: {audio_path}")
        except Exception as e:
            self.logger.warning(f"Failed to cleanup audio file {audio_path}: {str(e)}")
