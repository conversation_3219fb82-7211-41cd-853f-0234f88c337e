"""
Main video dubbing pipeline controller.
Orchestrates the entire dubbing process from video input to dubbed output.
"""

import logging
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from tqdm import tqdm

from .utils import (
    load_config, setup_logging, create_directories, get_video_files,
    print_progress, print_success, print_error, print_warning
)
from .audio_extractor import AudioExtractor
from .speech_to_text import SpeechToText
from .translator import Translator
from .text_to_speech import TextToSpeech
from .video_processor import VideoProcessor

class VideoDubbingPipeline:
    """Main pipeline for video dubbing process."""
    
    def __init__(self, config_path: str = 'config.yaml'):
        """Initialize the pipeline with configuration."""
        self.config = load_config(config_path)
        self.logger = setup_logging(self.config)
        
        # Create necessary directories
        create_directories(self.config)
        
        # Initialize components
        self.audio_extractor = AudioExtractor(self.config)
        self.speech_to_text = SpeechToText(self.config)
        self.translator = Translator(self.config)
        self.text_to_speech = TextToSpeech(self.config)
        self.video_processor = VideoProcessor(self.config)
        
        self.logger.info("Video dubbing pipeline initialized")
    
    def setup_translation(self) -> bool:
        """Set up translation models and packages."""
        print_progress("Setting up translation...")
        
        if self.config.get('translation', {}).get('model') == 'argos':
            return self.translator.setup_argos_translation()
        
        return True
    
    def process_single_video(self, video_path: Path) -> bool:
        """
        Process a single video file through the entire dubbing pipeline.
        
        Args:
            video_path: Path to input video file
            
        Returns:
            True if processing successful, False otherwise
        """
        start_time = time.time()
        
        print_progress(f"\n{'='*60}")
        print_progress(f"Processing video: {video_path.name}")
        print_progress(f"{'='*60}")
        
        try:
            # Step 1: Extract audio
            print_progress("Step 1/5: Extracting audio...")
            audio_path = self.audio_extractor.extract_audio(video_path)
            if audio_path is None:
                print_error("Audio extraction failed")
                return False
            
            # Step 2: Transcribe audio
            print_progress("Step 2/5: Transcribing audio...")
            segments = self.speech_to_text.transcribe_audio(audio_path)
            if segments is None or len(segments) == 0:
                print_error("Audio transcription failed")
                self.audio_extractor.cleanup_temp_audio(audio_path)
                return False
            
            # Save original transcription
            transcription_path = self.config['temp_dir'] / f"{video_path.stem}_transcription.txt"
            full_text = self.speech_to_text.get_full_text(segments)
            with open(transcription_path, 'w', encoding='utf-8') as f:
                f.write(full_text)
            
            print_success(f"Transcription saved: {transcription_path}")
            
            # Step 3: Translate text
            print_progress("Step 3/5: Translating text...")
            translated_segments = self.translator.translate_segments(segments)
            if translated_segments is None:
                print_error("Translation failed")
                self.audio_extractor.cleanup_temp_audio(audio_path)
                return False
            
            # Save translated text
            translation_path = self.config['temp_dir'] / f"{video_path.stem}_translation.txt"
            translated_text = self.speech_to_text.get_full_text(translated_segments)
            with open(translation_path, 'w', encoding='utf-8') as f:
                f.write(translated_text)
            
            print_success(f"Translation saved: {translation_path}")
            
            # Step 4: Generate speech
            print_progress("Step 4/5: Generating dubbed audio...")
            
            # Extract speaker sample for voice cloning
            speaker_sample = self.text_to_speech.extract_speaker_sample(audio_path)
            
            # Synthesize speech for all segments
            audio_files = self.text_to_speech.synthesize_segments(
                translated_segments, speaker_sample
            )
            
            if audio_files is None:
                print_error("Speech synthesis failed")
                self.audio_extractor.cleanup_temp_audio(audio_path)
                return False
            
            # Combine audio segments
            dubbed_audio_path = Path(self.config['temp_dir']) / f"{video_path.stem}_dubbed_audio.wav"
            if not self.text_to_speech.combine_audio_segments(
                audio_files, translated_segments, dubbed_audio_path
            ):
                print_error("Audio combination failed")
                self.text_to_speech.cleanup_segment_files(audio_files)
                self.audio_extractor.cleanup_temp_audio(audio_path)
                return False
            
            # Step 5: Combine with video
            print_progress("Step 5/5: Creating final video...")
            output_path = self.video_processor.process_video(
                video_path, dubbed_audio_path
            )
            
            if output_path is None:
                print_error("Video processing failed")
                self.text_to_speech.cleanup_segment_files(audio_files)
                self.audio_extractor.cleanup_temp_audio(audio_path)
                return False
            
            # Validate output
            if not self.video_processor.validate_output(output_path):
                print_error("Output validation failed")
                return False
            
            # Cleanup temporary files
            self.text_to_speech.cleanup_segment_files(audio_files)
            self.audio_extractor.cleanup_temp_audio(audio_path)
            if speaker_sample:
                speaker_sample.unlink(missing_ok=True)
            dubbed_audio_path.unlink(missing_ok=True)
            
            # Success
            processing_time = time.time() - start_time
            print_success(f"Video processing completed successfully!")
            print_success(f"Output: {output_path}")
            print_success(f"Processing time: {processing_time:.1f} seconds")
            
            self.logger.info(f"Successfully processed {video_path.name} in {processing_time:.1f}s")
            return True
            
        except Exception as e:
            print_error(f"Unexpected error processing {video_path.name}: {str(e)}")
            self.logger.error(f"Unexpected error processing {video_path.name}: {str(e)}")
            return False
    
    def process_directory(self, input_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Process all video files in the input directory.
        
        Args:
            input_dir: Optional input directory path (uses config if not provided)
            
        Returns:
            Dictionary with processing results
        """
        input_directory = input_dir or self.config['input_dir']
        video_files = get_video_files(input_directory)
        
        if not video_files:
            print_warning(f"No video files found in {input_directory}")
            return {'processed': 0, 'failed': 0, 'total': 0}
        
        print_progress(f"Found {len(video_files)} video files to process")
        
        # Setup translation if needed
        if not self.setup_translation():
            print_error("Translation setup failed")
            return {'processed': 0, 'failed': len(video_files), 'total': len(video_files)}
        
        # Process each video
        processed = 0
        failed = 0
        
        for video_path in tqdm(video_files, desc="Processing videos"):
            if self.process_single_video(video_path):
                processed += 1
            else:
                failed += 1
                print_error(f"Failed to process: {video_path.name}")
        
        # Summary
        print_progress(f"\n{'='*60}")
        print_progress("PROCESSING SUMMARY")
        print_progress(f"{'='*60}")
        print_success(f"Successfully processed: {processed}/{len(video_files)} videos")
        
        if failed > 0:
            print_error(f"Failed to process: {failed}/{len(video_files)} videos")
        
        self.logger.info(f"Batch processing completed: {processed} successful, {failed} failed")
        
        return {
            'processed': processed,
            'failed': failed,
            'total': len(video_files)
        }
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get status of pipeline components."""
        status = {
            'ffmpeg_available': self.audio_extractor.check_ffmpeg(),
            'whisper_model_loaded': self.speech_to_text.model is not None,
            'tts_model_loaded': self.text_to_speech.tts_model is not None,
            'translation_ready': False
        }
        
        # Check translation readiness
        try:
            if self.config.get('translation', {}).get('model') == 'argos':
                supported_langs = self.translator.get_supported_languages()
                status['translation_ready'] = 'argos' in supported_langs
        except Exception:
            pass
        
        return status
