"""
Speech-to-text module using faster-whisper for transcription.
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from .utils import print_progress, print_success, print_error

try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    FASTER_WHISPER_AVAILABLE = False
    print_error("faster-whisper not installed. Please install it: pip install faster-whisper")

@dataclass
class TranscriptionSegment:
    """Represents a transcription segment with timing information."""
    start: float
    end: float
    text: str
    confidence: float = 0.0

class SpeechToText:
    """Handles speech-to-text transcription using faster-whisper."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize SpeechToText with configuration."""
        self.config = config
        self.whisper_config = config.get('whisper', {})
        self.logger = logging.getLogger('video_dubbing.speech_to_text')
        self.model = None
        
        if not FASTER_WHISPER_AVAILABLE:
            raise ImportError("faster-whisper is required but not installed")
    
    def load_model(self) -> bool:
        """Load the Whisper model."""
        try:
            model_size = self.whisper_config.get('model_size', 'base')
            device = self.whisper_config.get('device', 'cpu')
            compute_type = self.whisper_config.get('compute_type', 'int8')
            
            print_progress(f"Loading Whisper model: {model_size} on {device}")
            self.logger.info(f"Loading Whisper model: {model_size}, device: {device}, compute_type: {compute_type}")
            
            self.model = WhisperModel(
                model_size,
                device=device,
                compute_type=compute_type
            )
            
            print_success("Whisper model loaded successfully")
            self.logger.info("Whisper model loaded successfully")
            return True
            
        except Exception as e:
            print_error(f"Failed to load Whisper model: {str(e)}")
            self.logger.error(f"Failed to load Whisper model: {str(e)}")
            return False
    
    def transcribe_audio(self, audio_path: Path) -> Optional[List[TranscriptionSegment]]:
        """
        Transcribe audio file to text with timing information.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            List of transcription segments or None if transcription failed
        """
        if self.model is None:
            if not self.load_model():
                return None
        
        if not audio_path.exists():
            print_error(f"Audio file not found: {audio_path}")
            return None
        
        try:
            print_progress(f"Transcribing audio: {audio_path.name}")
            self.logger.info(f"Starting transcription of {audio_path}")
            
            # Get transcription parameters
            language = self.whisper_config.get('language', 'en')
            
            # Transcribe with faster-whisper
            segments, info = self.model.transcribe(
                str(audio_path),
                language=language,
                beam_size=5,
                word_timestamps=True
            )
            
            # Convert segments to our format
            transcription_segments = []
            for segment in segments:
                transcription_segments.append(
                    TranscriptionSegment(
                        start=segment.start,
                        end=segment.end,
                        text=segment.text.strip(),
                        confidence=getattr(segment, 'avg_logprob', 0.0)
                    )
                )
            
            print_success(f"Transcription completed. Found {len(transcription_segments)} segments")
            self.logger.info(f"Transcription completed: {len(transcription_segments)} segments, "
                           f"language: {info.language}, probability: {info.language_probability:.2f}")
            
            return transcription_segments
            
        except Exception as e:
            print_error(f"Transcription failed: {str(e)}")
            self.logger.error(f"Transcription failed: {str(e)}")
            return None
    
    def get_full_text(self, segments: List[TranscriptionSegment]) -> str:
        """
        Combine all transcription segments into full text.
        
        Args:
            segments: List of transcription segments
            
        Returns:
            Combined text
        """
        return " ".join(segment.text for segment in segments)
    
    def save_transcription(self, segments: List[TranscriptionSegment], 
                          output_path: Path, format: str = 'txt') -> bool:
        """
        Save transcription to file.
        
        Args:
            segments: List of transcription segments
            output_path: Path to save transcription
            format: Output format ('txt', 'srt', 'vtt')
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format == 'txt':
                with open(output_path, 'w', encoding='utf-8') as f:
                    for segment in segments:
                        f.write(f"{segment.text}\n")
            
            elif format == 'srt':
                with open(output_path, 'w', encoding='utf-8') as f:
                    for i, segment in enumerate(segments, 1):
                        start_time = self._format_timestamp(segment.start)
                        end_time = self._format_timestamp(segment.end)
                        f.write(f"{i}\n")
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{segment.text}\n\n")
            
            elif format == 'vtt':
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write("WEBVTT\n\n")
                    for segment in segments:
                        start_time = self._format_timestamp(segment.start, vtt=True)
                        end_time = self._format_timestamp(segment.end, vtt=True)
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{segment.text}\n\n")
            
            self.logger.info(f"Transcription saved to {output_path}")
            return True
            
        except Exception as e:
            print_error(f"Failed to save transcription: {str(e)}")
            self.logger.error(f"Failed to save transcription: {str(e)}")
            return False
    
    def _format_timestamp(self, seconds: float, vtt: bool = False) -> str:
        """Format timestamp for subtitle formats."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        if vtt:
            return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
        else:
            return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')
