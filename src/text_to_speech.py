"""
Text-to-speech module with voice cloning capabilities.
Uses Coqui TTS for high-quality speech synthesis.
"""

import logging
import torch
from pathlib import Path
from typing import Dict, Any, List, Optional
from .utils import print_progress, print_success, print_error, print_warning
from .speech_to_text import TranscriptionSegment

try:
    from TTS.api import TTS
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print_warning("TTS not installed. Please install: pip install TTS")

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print_warning("pydub not installed. Please install: pip install pydub")

class TextToSpeech:
    """Handles text-to-speech conversion with voice cloning."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize TextToSpeech with configuration."""
        self.config = config
        self.tts_config = config.get('tts', {})
        self.temp_dir = Path(config.get('temp_dir', 'temp'))
        self.logger = logging.getLogger('video_dubbing.text_to_speech')
        self.tts_model = None
        
        # Ensure temp directory exists
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        if not TTS_AVAILABLE:
            raise ImportError("TTS is required but not installed")
        if not PYDUB_AVAILABLE:
            raise ImportError("pydub is required but not installed")
    
    def load_model(self) -> bool:
        """Load the TTS model."""
        try:
            model_name = self.tts_config.get('model', 'tts_models/multilingual/multi-dataset/xtts_v2')
            
            print_progress(f"Loading TTS model: {model_name}")
            self.logger.info(f"Loading TTS model: {model_name}")
            
            # Check if CUDA is available
            device = "cuda" if torch.cuda.is_available() else "cpu"
            print_progress(f"Using device: {device}")
            
            self.tts_model = TTS(model_name).to(device)
            
            print_success("TTS model loaded successfully")
            self.logger.info("TTS model loaded successfully")
            return True
            
        except Exception as e:
            print_error(f"Failed to load TTS model: {str(e)}")
            self.logger.error(f"Failed to load TTS model: {str(e)}")
            return False
    
    def synthesize_speech(self, text: str, output_path: Path, 
                         speaker_wav: Optional[Path] = None) -> bool:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to synthesize
            output_path: Path to save generated audio
            speaker_wav: Optional path to speaker reference audio for voice cloning
            
        Returns:
            True if synthesis successful, False otherwise
        """
        if self.tts_model is None:
            if not self.load_model():
                return False
        
        if not text.strip():
            print_warning("Empty text provided for synthesis")
            return False
        
        try:
            # Prepare synthesis parameters
            language = self.tts_config.get('language', 'hi')
            speed = self.tts_config.get('speed', 1.0)
            
            # Use speaker reference if provided
            speaker_wav_path = speaker_wav or self.tts_config.get('speaker_wav')
            
            print_progress(f"Synthesizing speech: {text[:50]}...")
            self.logger.info(f"Synthesizing speech, length: {len(text)} chars")
            
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            if speaker_wav_path and Path(speaker_wav_path).exists():
                # Voice cloning mode
                self.tts_model.tts_to_file(
                    text=text,
                    file_path=str(output_path),
                    speaker_wav=str(speaker_wav_path),
                    language=language,
                    speed=speed
                )
            else:
                # Standard TTS mode
                self.tts_model.tts_to_file(
                    text=text,
                    file_path=str(output_path),
                    language=language,
                    speed=speed
                )
            
            if output_path.exists():
                print_success(f"Speech synthesized: {output_path}")
                self.logger.info(f"Speech synthesis successful: {output_path}")
                return True
            else:
                print_error("Speech synthesis failed - no output file generated")
                return False
                
        except Exception as e:
            print_error(f"Speech synthesis failed: {str(e)}")
            self.logger.error(f"Speech synthesis failed: {str(e)}")
            return False
    
    def synthesize_segments(self, segments: List[TranscriptionSegment], 
                           speaker_wav: Optional[Path] = None) -> Optional[List[Path]]:
        """
        Synthesize speech for multiple segments.
        
        Args:
            segments: List of transcription segments
            speaker_wav: Optional path to speaker reference audio
            
        Returns:
            List of paths to generated audio files or None if failed
        """
        if not segments:
            return []
        
        print_progress(f"Synthesizing speech for {len(segments)} segments")
        
        audio_files = []
        failed_count = 0
        
        for i, segment in enumerate(segments):
            if i % 5 == 0:  # Progress update every 5 segments
                print_progress(f"Synthesizing segment {i+1}/{len(segments)}")
            
            # Generate unique filename for each segment
            segment_filename = f"segment_{i:04d}_{segment.start:.2f}s.wav"
            segment_path = self.temp_dir / segment_filename
            
            if self.synthesize_speech(segment.text, segment_path, speaker_wav):
                audio_files.append(segment_path)
            else:
                failed_count += 1
                audio_files.append(None)
                self.logger.warning(f"Failed to synthesize segment {i}: {segment.text[:50]}...")
        
        if failed_count > 0:
            print_warning(f"Failed to synthesize {failed_count} segments")
        
        print_success(f"Speech synthesis completed. {len(audio_files) - failed_count}/{len(segments)} segments synthesized")
        
        return audio_files
    
    def combine_audio_segments(self, audio_files: List[Optional[Path]], 
                              segments: List[TranscriptionSegment],
                              output_path: Path) -> bool:
        """
        Combine individual audio segments into a single audio file with proper timing.
        
        Args:
            audio_files: List of paths to audio segment files
            segments: List of transcription segments with timing
            output_path: Path to save combined audio
            
        Returns:
            True if combination successful, False otherwise
        """
        try:
            print_progress("Combining audio segments...")
            
            if len(audio_files) != len(segments):
                print_error("Mismatch between audio files and segments")
                return False
            
            # Calculate total duration
            total_duration = max(segment.end for segment in segments) if segments else 0
            
            # Create silent audio as base
            combined_audio = AudioSegment.silent(duration=int(total_duration * 1000))  # pydub uses milliseconds
            
            for i, (audio_file, segment) in enumerate(zip(audio_files, segments)):
                if audio_file is None or not audio_file.exists():
                    self.logger.warning(f"Skipping missing audio file for segment {i}")
                    continue
                
                try:
                    # Load segment audio
                    segment_audio = AudioSegment.from_wav(str(audio_file))
                    
                    # Calculate timing
                    start_ms = int(segment.start * 1000)
                    end_ms = int(segment.end * 1000)
                    expected_duration = end_ms - start_ms
                    
                    # Adjust audio duration to match expected timing
                    if len(segment_audio) > expected_duration:
                        # Speed up if too long
                        speedup_factor = len(segment_audio) / expected_duration
                        segment_audio = segment_audio.speedup(playback_speed=speedup_factor)
                    elif len(segment_audio) < expected_duration:
                        # Add silence if too short
                        silence_needed = expected_duration - len(segment_audio)
                        segment_audio = segment_audio + AudioSegment.silent(duration=silence_needed)
                    
                    # Overlay the segment audio at the correct position
                    combined_audio = combined_audio.overlay(segment_audio, position=start_ms)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to process segment {i}: {str(e)}")
                    continue
            
            # Export combined audio
            output_path.parent.mkdir(parents=True, exist_ok=True)
            combined_audio.export(str(output_path), format="wav")
            
            print_success(f"Audio segments combined: {output_path}")
            self.logger.info(f"Audio combination successful: {output_path}")
            return True
            
        except Exception as e:
            print_error(f"Failed to combine audio segments: {str(e)}")
            self.logger.error(f"Failed to combine audio segments: {str(e)}")
            return False
    
    def cleanup_segment_files(self, audio_files: List[Optional[Path]]) -> None:
        """Clean up temporary segment audio files."""
        for audio_file in audio_files:
            if audio_file and audio_file.exists():
                try:
                    audio_file.unlink()
                except Exception as e:
                    self.logger.warning(f"Failed to cleanup {audio_file}: {str(e)}")
    
    def extract_speaker_sample(self, audio_path: Path, 
                              duration: float = 10.0) -> Optional[Path]:
        """
        Extract a sample from the original audio for voice cloning.
        
        Args:
            audio_path: Path to original audio file
            duration: Duration of sample to extract (seconds)
            
        Returns:
            Path to extracted sample or None if failed
        """
        try:
            sample_path = self.temp_dir / f"speaker_sample_{audio_path.stem}.wav"
            
            # Load audio and extract sample
            audio = AudioSegment.from_wav(str(audio_path))
            
            # Extract from the beginning (first few seconds usually have clear speech)
            sample_duration_ms = int(duration * 1000)
            sample = audio[:sample_duration_ms]
            
            # Export sample
            sample.export(str(sample_path), format="wav")
            
            self.logger.info(f"Speaker sample extracted: {sample_path}")
            return sample_path
            
        except Exception as e:
            self.logger.error(f"Failed to extract speaker sample: {str(e)}")
            return None
