"""
Translation module for converting text between languages.
Uses local translation models for privacy and offline operation.
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from .utils import print_progress, print_success, print_error, print_warning
from .speech_to_text import TranscriptionSegment

try:
    import argostranslate.package
    import argostranslate.translate
    ARGOS_AVAILABLE = True
except ImportError:
    ARGOS_AVAILABLE = False
    print_warning("argostranslate not installed. Translation will be limited.")

class Translator:
    """Handles text translation using local models."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Translator with configuration."""
        self.config = config
        self.translation_config = config.get('translation', {})
        self.logger = logging.getLogger('video_dubbing.translator')
        self.source_lang = self.translation_config.get('source_lang', 'en')
        self.target_lang = self.translation_config.get('target_lang', 'hi')
        self.model_type = self.translation_config.get('model', 'argos')
        
        # Language code mappings for different services
        self.lang_mappings = {
            'argos': {
                'en': 'en',
                'hi': 'hi',
                'es': 'es',
                'fr': 'fr',
                'de': 'de',
                'it': 'it',
                'pt': 'pt',
                'ru': 'ru',
                'ja': 'ja',
                'ko': 'ko',
                'zh': 'zh'
            }
        }
    
    def setup_argos_translation(self) -> bool:
        """Set up Argos Translate with required language packages."""
        if not ARGOS_AVAILABLE:
            print_error("argostranslate not available. Please install: pip install argostranslate")
            return False
        
        try:
            # Update package index
            print_progress("Updating Argos Translate package index...")
            argostranslate.package.update_package_index()
            
            # Get available packages
            available_packages = argostranslate.package.get_available_packages()
            
            # Find required package
            source_code = self.lang_mappings['argos'].get(self.source_lang)
            target_code = self.lang_mappings['argos'].get(self.target_lang)
            
            if not source_code or not target_code:
                print_error(f"Language pair {self.source_lang}->{self.target_lang} not supported")
                return False
            
            # Look for the translation package
            package_to_install = None
            for package in available_packages:
                if (package.from_code == source_code and 
                    package.to_code == target_code):
                    package_to_install = package
                    break
            
            if package_to_install is None:
                print_error(f"Translation package {source_code}->{target_code} not found")
                return False
            
            # Check if package is already installed
            installed_packages = argostranslate.package.get_installed_packages()
            for package in installed_packages:
                if (package.from_code == source_code and 
                    package.to_code == target_code):
                    print_success(f"Translation package {source_code}->{target_code} already installed")
                    return True
            
            # Install the package
            print_progress(f"Installing translation package {source_code}->{target_code}...")
            argostranslate.package.install_from_path(package_to_install.download())
            print_success(f"Translation package {source_code}->{target_code} installed successfully")
            
            return True
            
        except Exception as e:
            print_error(f"Failed to setup Argos translation: {str(e)}")
            self.logger.error(f"Failed to setup Argos translation: {str(e)}")
            return False
    
    def translate_text(self, text: str) -> Optional[str]:
        """
        Translate text from source language to target language.
        
        Args:
            text: Text to translate
            
        Returns:
            Translated text or None if translation failed
        """
        if not text.strip():
            return text
        
        if self.model_type == 'argos':
            return self._translate_with_argos(text)
        else:
            print_error(f"Unsupported translation model: {self.model_type}")
            return None
    
    def _translate_with_argos(self, text: str) -> Optional[str]:
        """Translate text using Argos Translate."""
        if not ARGOS_AVAILABLE:
            return None
        
        try:
            source_code = self.lang_mappings['argos'].get(self.source_lang)
            target_code = self.lang_mappings['argos'].get(self.target_lang)
            
            translated = argostranslate.translate.translate(text, source_code, target_code)
            return translated
            
        except Exception as e:
            self.logger.error(f"Argos translation failed: {str(e)}")
            return None
    
    def translate_segments(self, segments: List[TranscriptionSegment]) -> Optional[List[TranscriptionSegment]]:
        """
        Translate transcription segments while preserving timing.
        
        Args:
            segments: List of transcription segments
            
        Returns:
            List of translated segments or None if translation failed
        """
        if not segments:
            return segments
        
        print_progress(f"Translating {len(segments)} segments from {self.source_lang} to {self.target_lang}")
        
        translated_segments = []
        failed_count = 0
        
        for i, segment in enumerate(segments):
            if i % 10 == 0:  # Progress update every 10 segments
                print_progress(f"Translating segment {i+1}/{len(segments)}")
            
            translated_text = self.translate_text(segment.text)
            
            if translated_text is not None:
                translated_segments.append(
                    TranscriptionSegment(
                        start=segment.start,
                        end=segment.end,
                        text=translated_text,
                        confidence=segment.confidence
                    )
                )
            else:
                # Keep original text if translation fails
                translated_segments.append(segment)
                failed_count += 1
                self.logger.warning(f"Translation failed for segment: {segment.text[:50]}...")
        
        if failed_count > 0:
            print_warning(f"Translation failed for {failed_count} segments")
        
        print_success(f"Translation completed. {len(translated_segments) - failed_count}/{len(segments)} segments translated successfully")
        
        return translated_segments
    
    def translate_full_text(self, text: str) -> Optional[str]:
        """
        Translate full text by splitting into chunks if necessary.
        
        Args:
            text: Full text to translate
            
        Returns:
            Translated text or None if translation failed
        """
        if not text.strip():
            return text
        
        # Split long text into chunks to avoid API limits
        max_chunk_size = 1000  # characters
        
        if len(text) <= max_chunk_size:
            return self.translate_text(text)
        
        # Split into sentences and group into chunks
        sentences = text.split('. ')
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 2 <= max_chunk_size:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # Translate each chunk
        translated_chunks = []
        for i, chunk in enumerate(chunks):
            print_progress(f"Translating chunk {i+1}/{len(chunks)}")
            translated_chunk = self.translate_text(chunk)
            if translated_chunk:
                translated_chunks.append(translated_chunk)
            else:
                translated_chunks.append(chunk)  # Keep original if translation fails
        
        return " ".join(translated_chunks)
    
    def get_supported_languages(self) -> Dict[str, List[str]]:
        """Get list of supported languages for each translation service."""
        supported = {}
        
        if ARGOS_AVAILABLE:
            try:
                installed_packages = argostranslate.package.get_installed_packages()
                argos_langs = set()
                for package in installed_packages:
                    argos_langs.add(package.from_code)
                    argos_langs.add(package.to_code)
                supported['argos'] = sorted(list(argos_langs))
            except Exception:
                supported['argos'] = list(self.lang_mappings['argos'].keys())
        
        return supported
