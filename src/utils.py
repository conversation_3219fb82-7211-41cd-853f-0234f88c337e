"""
Utility functions for the video dubbing pipeline.
"""

import os
import logging
import yaml
from pathlib import Path
from typing import Dict, Any
import colorama
from colorama import Fore, Style

# Initialize colorama for cross-platform colored output
colorama.init()

def setup_logging(config: Dict[str, Any]) -> logging.Logger:
    """Set up logging configuration."""
    log_config = config.get('logging', {})
    
    # Create logs directory if it doesn't exist
    log_file = log_config.get('file', 'dubbing_pipeline.log')
    log_dir = Path(log_file).parent
    log_dir.mkdir(exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, log_config.get('level', 'INFO')),
        format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('video_dubbing')

def load_config(config_path: str = 'config.yaml') -> Dict[str, Any]:
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r') as file:
            config = yaml.safe_load(file)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"Configuration file {config_path} not found")
    except yaml.YAMLError as e:
        raise ValueError(f"Error parsing configuration file: {e}")

def create_directories(config: Dict[str, Any]) -> None:
    """Create necessary directories."""
    dirs_to_create = [
        config['input_dir'],
        config['output_dir'],
        config['temp_dir']
    ]
    
    for dir_path in dirs_to_create:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

def get_video_files(input_dir: str) -> list:
    """Get list of video files from input directory."""
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
    video_files = []
    
    input_path = Path(input_dir)
    if not input_path.exists():
        return video_files
    
    for file_path in input_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in video_extensions:
            video_files.append(file_path)
    
    return sorted(video_files)

def print_progress(message: str, color: str = Fore.CYAN) -> None:
    """Print colored progress message."""
    print(f"{color}[PROGRESS]{Style.RESET_ALL} {message}")

def print_success(message: str) -> None:
    """Print success message."""
    print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} {message}")

def print_error(message: str) -> None:
    """Print error message."""
    print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {message}")

def print_warning(message: str) -> None:
    """Print warning message."""
    print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {message}")

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for cross-platform compatibility."""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename

def get_output_filename(input_file: Path, suffix: str = "_hindi_dubbed") -> str:
    """Generate output filename with suffix."""
    stem = input_file.stem
    extension = input_file.suffix
    return f"{sanitize_filename(stem)}{suffix}{extension}"
