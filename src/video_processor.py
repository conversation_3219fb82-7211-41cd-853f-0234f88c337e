"""
Video processing module for combining dubbed audio with original video.
"""

import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from .utils import print_progress, print_success, print_error, get_output_filename

try:
    from moviepy.editor import VideoFileClip, AudioFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print_error("moviepy not installed. Please install: pip install moviepy")

class VideoProcessor:
    """Handles video processing and audio replacement."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize VideoProcessor with configuration."""
        self.config = config
        self.video_config = config.get('video', {})
        self.output_dir = Path(config.get('output_dir', 'output_videos'))
        self.temp_dir = Path(config.get('temp_dir', 'temp'))
        self.logger = logging.getLogger('video_dubbing.video_processor')
        
        # Ensure directories exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def check_ffmpeg(self) -> bool:
        """Check if ffmpeg is available."""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def combine_video_audio_ffmpeg(self, video_path: Path, audio_path: Path, 
                                  output_path: Path) -> bool:
        """
        Combine video and audio using ffmpeg (faster and more reliable).
        
        Args:
            video_path: Path to original video file
            audio_path: Path to new audio file
            output_path: Path to save output video
            
        Returns:
            True if combination successful, False otherwise
        """
        if not self.check_ffmpeg():
            print_error("ffmpeg not found. Please install ffmpeg.")
            return False
        
        try:
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Get configuration
            audio_codec = self.video_config.get('audio_codec', 'aac')
            video_codec = self.video_config.get('video_codec', 'copy')
            
            # Build ffmpeg command
            cmd = [
                'ffmpeg',
                '-i', str(video_path),  # Input video
                '-i', str(audio_path),  # Input audio
                '-c:v', video_codec,    # Video codec (copy = no re-encoding)
                '-c:a', audio_codec,    # Audio codec
                '-map', '0:v:0',        # Map video from first input
                '-map', '1:a:0',        # Map audio from second input
                '-shortest',            # End when shortest stream ends
                '-y',                   # Overwrite output file
                str(output_path)
            ]
            
            print_progress(f"Combining video and audio: {output_path.name}")
            self.logger.info(f"ffmpeg command: {' '.join(cmd)}")
            
            # Run ffmpeg
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes timeout
            )
            
            if result.returncode == 0:
                print_success(f"Video processing completed: {output_path}")
                self.logger.info(f"Video processing successful: {output_path}")
                return True
            else:
                print_error(f"ffmpeg failed: {result.stderr}")
                self.logger.error(f"ffmpeg error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print_error("Video processing timed out")
            self.logger.error("Video processing timed out")
            return False
        except Exception as e:
            print_error(f"Video processing error: {str(e)}")
            self.logger.error(f"Video processing error: {str(e)}")
            return False
    
    def combine_video_audio_moviepy(self, video_path: Path, audio_path: Path, 
                                   output_path: Path) -> bool:
        """
        Combine video and audio using moviepy (fallback method).
        
        Args:
            video_path: Path to original video file
            audio_path: Path to new audio file
            output_path: Path to save output video
            
        Returns:
            True if combination successful, False otherwise
        """
        if not MOVIEPY_AVAILABLE:
            print_error("moviepy not available")
            return False
        
        try:
            print_progress(f"Combining video and audio with moviepy: {output_path.name}")
            
            # Load video and audio
            video_clip = VideoFileClip(str(video_path))
            audio_clip = AudioFileClip(str(audio_path))
            
            # Ensure audio duration matches video duration
            if audio_clip.duration > video_clip.duration:
                audio_clip = audio_clip.subclip(0, video_clip.duration)
            elif audio_clip.duration < video_clip.duration:
                # Extend audio with silence if needed
                from moviepy.audio.AudioClip import AudioClip
                silence_duration = video_clip.duration - audio_clip.duration
                silence = AudioClip(lambda t: [0, 0], duration=silence_duration)
                audio_clip = audio_clip.concatenate_audioclips([audio_clip, silence])
            
            # Set the audio of the video clip
            final_video = video_clip.set_audio(audio_clip)
            
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write the result
            final_video.write_videofile(
                str(output_path),
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(self.temp_dir / 'temp_audio.m4a'),
                remove_temp=True,
                verbose=False,
                logger=None
            )
            
            # Clean up
            video_clip.close()
            audio_clip.close()
            final_video.close()
            
            print_success(f"Video processing completed: {output_path}")
            self.logger.info(f"Video processing successful: {output_path}")
            return True
            
        except Exception as e:
            print_error(f"MoviePy video processing error: {str(e)}")
            self.logger.error(f"MoviePy video processing error: {str(e)}")
            return False
    
    def process_video(self, video_path: Path, audio_path: Path, 
                     suffix: str = "_hindi_dubbed") -> Optional[Path]:
        """
        Process video by replacing audio with dubbed version.
        
        Args:
            video_path: Path to original video file
            audio_path: Path to dubbed audio file
            suffix: Suffix to add to output filename
            
        Returns:
            Path to processed video or None if processing failed
        """
        if not video_path.exists():
            print_error(f"Video file not found: {video_path}")
            return None
        
        if not audio_path.exists():
            print_error(f"Audio file not found: {audio_path}")
            return None
        
        # Generate output filename
        output_filename = get_output_filename(video_path, suffix)
        output_path = self.output_dir / output_filename
        
        # Try ffmpeg first (preferred method)
        if self.combine_video_audio_ffmpeg(video_path, audio_path, output_path):
            return output_path
        
        # Fallback to moviepy
        print_progress("ffmpeg failed, trying moviepy...")
        if self.combine_video_audio_moviepy(video_path, audio_path, output_path):
            return output_path
        
        print_error("Both ffmpeg and moviepy failed")
        return None
    
    def get_video_info(self, video_path: Path) -> Optional[Dict[str, Any]]:
        """
        Get video information using ffprobe.
        
        Args:
            video_path: Path to video file
            
        Returns:
            Dictionary with video information or None if failed
        """
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            str(video_path)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                
                # Extract relevant information
                format_info = data.get('format', {})
                video_stream = None
                audio_stream = None
                
                for stream in data.get('streams', []):
                    if stream.get('codec_type') == 'video' and video_stream is None:
                        video_stream = stream
                    elif stream.get('codec_type') == 'audio' and audio_stream is None:
                        audio_stream = stream
                
                return {
                    'duration': float(format_info.get('duration', 0)),
                    'size': int(format_info.get('size', 0)),
                    'video': {
                        'codec': video_stream.get('codec_name') if video_stream else None,
                        'width': int(video_stream.get('width', 0)) if video_stream else 0,
                        'height': int(video_stream.get('height', 0)) if video_stream else 0,
                        'fps': eval(video_stream.get('r_frame_rate', '0/1')) if video_stream else 0
                    },
                    'audio': {
                        'codec': audio_stream.get('codec_name') if audio_stream else None,
                        'sample_rate': int(audio_stream.get('sample_rate', 0)) if audio_stream else 0,
                        'channels': int(audio_stream.get('channels', 0)) if audio_stream else 0
                    }
                }
            return None
        except Exception as e:
            self.logger.error(f"Error getting video info: {str(e)}")
            return None
    
    def validate_output(self, output_path: Path) -> bool:
        """
        Validate that the output video was created successfully.
        
        Args:
            output_path: Path to output video
            
        Returns:
            True if video is valid, False otherwise
        """
        if not output_path.exists():
            return False
        
        # Check if file has reasonable size
        if output_path.stat().st_size < 1024:  # Less than 1KB
            return False
        
        # Try to get video info
        info = self.get_video_info(output_path)
        if info is None:
            return False
        
        # Check if video has both video and audio streams
        has_video = info['video']['codec'] is not None
        has_audio = info['audio']['codec'] is not None
        
        return has_video and has_audio
