#!/usr/bin/env python3
"""
Test script for the video dubbing pipeline.
Tests individual components and the full pipeline.
"""

import sys
import tempfile
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.utils import print_progress, print_success, print_error, load_config
from src.pipeline import VideoDubbingPipeline

def test_dependencies():
    """Test if all required dependencies are available."""
    print_progress("Testing dependencies...")
    
    missing = []
    
    try:
        import faster_whisper
        print_success("✓ faster-whisper available")
    except ImportError:
        missing.append("faster-whisper")
        print_error("✗ faster-whisper not available")
    
    try:
        import TTS
        print_success("✓ TTS available")
    except ImportError:
        missing.append("TTS")
        print_error("✗ TTS not available")
    
    try:
        import argostranslate
        print_success("✓ argostranslate available")
    except ImportError:
        missing.append("argostranslate")
        print_error("✗ argostranslate not available")
    
    try:
        import moviepy
        print_success("✓ moviepy available")
    except ImportError:
        missing.append("moviepy")
        print_error("✗ moviepy not available")
    
    try:
        import pydub
        print_success("✓ pydub available")
    except ImportError:
        missing.append("pydub")
        print_error("✗ pydub not available")
    
    return len(missing) == 0, missing

def test_ffmpeg():
    """Test FFmpeg availability."""
    print_progress("Testing FFmpeg...")
    
    import subprocess
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("✓ FFmpeg available")
            return True
        else:
            print_error("✗ FFmpeg not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("✗ FFmpeg not found")
        return False

def test_configuration():
    """Test configuration loading."""
    print_progress("Testing configuration...")
    
    try:
        config = load_config('config.yaml')
        print_success("✓ Configuration loaded successfully")
        
        # Check required keys
        required_keys = ['input_dir', 'output_dir', 'temp_dir', 'whisper', 'translation', 'tts']
        for key in required_keys:
            if key in config:
                print_success(f"  ✓ {key} configured")
            else:
                print_error(f"  ✗ {key} missing from configuration")
                return False
        
        return True
    except Exception as e:
        print_error(f"✗ Configuration error: {e}")
        return False

def test_pipeline_initialization():
    """Test pipeline initialization."""
    print_progress("Testing pipeline initialization...")
    
    try:
        pipeline = VideoDubbingPipeline('config.yaml')
        print_success("✓ Pipeline initialized successfully")
        
        # Test status
        status = pipeline.get_pipeline_status()
        print_progress("Pipeline component status:")
        for component, available in status.items():
            if available:
                print_success(f"  ✓ {component}")
            else:
                print_error(f"  ✗ {component}")
        
        return True
    except Exception as e:
        print_error(f"✗ Pipeline initialization failed: {e}")
        return False

def create_test_video():
    """Create a simple test video for testing."""
    print_progress("Creating test video...")
    
    try:
        import subprocess
        
        # Create a simple 5-second test video with audio
        test_video_path = Path("test_video.mp4")
        
        cmd = [
            'ffmpeg',
            '-f', 'lavfi',
            '-i', 'testsrc2=duration=5:size=320x240:rate=30',
            '-f', 'lavfi', 
            '-i', 'sine=frequency=1000:duration=5',
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-y',
            str(test_video_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0 and test_video_path.exists():
            print_success(f"✓ Test video created: {test_video_path}")
            return test_video_path
        else:
            print_error("✗ Failed to create test video")
            return None
            
    except Exception as e:
        print_error(f"✗ Test video creation failed: {e}")
        return None

def main():
    """Run all tests."""
    print_progress("🧪 Video Dubbing Pipeline Test Suite")
    print_progress("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Dependencies
    deps_ok, missing = test_dependencies()
    if deps_ok:
        tests_passed += 1
    else:
        print_error(f"Missing dependencies: {', '.join(missing)}")
        print_progress("Install with: pip install -r requirements.txt")
    
    print_progress("-" * 30)
    
    # Test 2: FFmpeg
    if test_ffmpeg():
        tests_passed += 1
    
    print_progress("-" * 30)
    
    # Test 3: Configuration
    if test_configuration():
        tests_passed += 1
    
    print_progress("-" * 30)
    
    # Test 4: Pipeline initialization
    if deps_ok and test_pipeline_initialization():
        tests_passed += 1
    
    print_progress("-" * 30)
    
    # Summary
    print_progress("Test Summary:")
    print_success(f"Passed: {tests_passed}/{total_tests} tests")
    
    if tests_passed == total_tests:
        print_success("🎉 All tests passed! Pipeline is ready to use.")
        
        # Offer to create test video
        try:
            response = input("\nCreate a test video for pipeline testing? (y/n): ")
            if response.lower() in ['y', 'yes']:
                test_video = create_test_video()
                if test_video:
                    print_progress(f"\nTo test the full pipeline, run:")
                    print_progress(f"python main.py --single {test_video}")
        except KeyboardInterrupt:
            pass
        
        return 0
    else:
        print_error("❌ Some tests failed. Please fix the issues before using the pipeline.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
